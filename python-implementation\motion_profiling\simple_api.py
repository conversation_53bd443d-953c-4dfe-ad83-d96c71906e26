"""
Simple API for motion profiling - equivalent to the Rust API.
"""

from typing import Optional
from utils.robot import Pose
from utils.bezier import Bezier
from .motion_profile import MotionProfile, Path
from .path_segment import PathSegment, Constraints
from config.constants import INCHES_TO_METERS, METERS_TO_INCHES


# Global motion profile instance
_current_motion_profile: Optional[MotionProfile] = None


def create_path_from_beziers(beziers: list, start_speed: float, end_speed: float) -> Path:
    """
    Create a Path object from a list of Bezier curves.
    
    Args:
        beziers: List of Bezier objects
        start_speed: Starting speed in m/s
        end_speed: Ending speed in m/s
        
    Returns:
        Path object
    """
    segments = []
    
    for bezier in beziers:
        # Convert Bezier to PathSegment
        path_points = [bezier.p1, bezier.p2, bezier.p3, bezier.p4]
        constraints = Constraints(
            velocity=bezier.path_max_speed * INCHES_TO_METERS,
            accel=bezier.path_max_accel * INCHES_TO_METERS
        )
        
        segment = PathSegment(
            inverted=bezier.reversed,
            stop_end=bezier.stop_end,
            path=path_points,
            constraints=constraints
        )
        segments.append(segment)
    
    return Path(start_speed, end_speed, segments)


def get_duration(path: Path) -> int:
    """
    Get the duration of a path in milliseconds.
    
    Args:
        path: Path object
        
    Returns:
        Duration in milliseconds
    """
    global _current_motion_profile
    
    try:
        _current_motion_profile = MotionProfile(path)
        return _current_motion_profile.get_duration_ms()
    except Exception:
        # Return a large duration if motion profiling fails
        return 1000000000  # 1000 seconds


def get_pose(t: float) -> Pose:
    """
    Get the robot pose at time t.
    
    Args:
        t: Time in seconds
        
    Returns:
        Pose object with x, y, theta
    """
    global _current_motion_profile
    
    if _current_motion_profile is None:
        return Pose(0.0, 0.0, 0.0)
    
    command = _current_motion_profile.get_command_at_time(int(t * 1000))
    if command is None:
        return Pose(0.0, 0.0, 0.0)
    
    return command.desired_pose


def get_velocity(t: float) -> float:
    """
    Get the robot velocity at time t.
    
    Args:
        t: Time in seconds
        
    Returns:
        Velocity in inches per second
    """
    global _current_motion_profile
    
    if _current_motion_profile is None:
        return 0.0
    
    command = _current_motion_profile.get_command_at_time(int(t * 1000))
    if command is None:
        return 0.0
    
    # Convert from m/s to in/s
    return command.desired_velocity * METERS_TO_INCHES


def get_angular_velocity(t: float) -> float:
    """
    Get the robot angular velocity at time t.
    
    Args:
        t: Time in seconds
        
    Returns:
        Angular velocity in degrees per second
    """
    global _current_motion_profile
    
    if _current_motion_profile is None:
        return 0.0
    
    command = _current_motion_profile.get_command_at_time(int(t * 1000))
    if command is None:
        return 0.0
    
    # Convert from rad/s to deg/s
    import math
    return math.degrees(command.desired_angular)


def reset_motion_profile():
    """Reset the current motion profile."""
    global _current_motion_profile
    _current_motion_profile = None
