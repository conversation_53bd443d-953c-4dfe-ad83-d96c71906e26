"""
Velocity and acceleration controls for path segments.
"""

import tkinter as tk
from tkinter import ttk
from typing import List, Callable, Optional

from utils.bezier import Bezier
from config.constants import MAX_SPEED, MAX_ACCEL, DEFAULT_MAX_SPEED, DEFAULT_MAX_ACCEL


class VelocityControls(ttk.Frame):
    """
    Controls for managing velocity and acceleration constraints of path segments.
    """
    
    def __init__(self, parent, on_change_callback: Optional[Callable] = None):
        """
        Initialize velocity controls.
        
        Args:
            parent: Parent widget
            on_change_callback: Callback when bezier properties change
        """
        super().__init__(parent)
        self.on_change_callback = on_change_callback
        self.beziers: List[Bezier] = []
        
        # Default values
        self.default_max_speed = DEFAULT_MAX_SPEED
        self.default_max_accel = DEFAULT_MAX_ACCEL
        self.all_visible = True
        
        self._setup_layout()
    
    def _setup_layout(self):
        """Set up the control layout."""
        # Title and time display
        self.title_label = ttk.Label(self, text="Velocity, Accel. Time: 0.0s", font=("Arial", 12, "bold"))
        self.title_label.pack(pady=(0, 10))
        
        # Global controls frame
        global_frame = ttk.Frame(self)
        global_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Visibility toggle
        self.visibility_var = tk.BooleanVar(value=self.all_visible)
        self.visibility_button = ttk.Checkbutton(
            global_frame,
            text="Show All",
            variable=self.visibility_var,
            command=self._on_visibility_toggle
        )
        self.visibility_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # Default controls
        ttk.Label(global_frame, text="Default:").pack(side=tk.LEFT)
        
        # Default speed
        self.default_speed_var = tk.DoubleVar(value=self.default_max_speed)
        self.default_speed_scale = ttk.Scale(
            global_frame,
            from_=1.0,
            to=MAX_SPEED,
            variable=self.default_speed_var,
            command=self._on_default_speed_change,
            length=100
        )
        self.default_speed_scale.pack(side=tk.LEFT, padx=2)
        self.default_speed_label = ttk.Label(global_frame, text=f"{self.default_max_speed:.0f}")
        self.default_speed_label.pack(side=tk.LEFT, padx=2)
        
        # Default acceleration
        self.default_accel_var = tk.DoubleVar(value=self.default_max_accel)
        self.default_accel_scale = ttk.Scale(
            global_frame,
            from_=1.0,
            to=MAX_ACCEL,
            variable=self.default_accel_var,
            command=self._on_default_accel_change,
            length=100
        )
        self.default_accel_scale.pack(side=tk.LEFT, padx=2)
        self.default_accel_label = ttk.Label(global_frame, text=f"{self.default_max_accel:.0f}")
        self.default_accel_label.pack(side=tk.LEFT, padx=2)
        
        # Scrollable frame for individual bezier controls
        self.canvas = tk.Canvas(self, height=300)
        self.scrollbar = ttk.Scrollbar(self, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")
        
        # Bind mousewheel
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)
    
    def _on_mousewheel(self, event):
        """Handle mouse wheel scrolling."""
        self.canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
    
    def _on_visibility_toggle(self):
        """Handle visibility toggle for all beziers."""
        self.all_visible = self.visibility_var.get()
        for bezier in self.beziers:
            bezier.visible = self.all_visible
        self._notify_change()
    
    def _on_default_speed_change(self, value):
        """Handle default speed change."""
        self.default_max_speed = float(value)
        self.default_speed_label.config(text=f"{self.default_max_speed:.0f}")
        
        # Apply to all beziers
        for bezier in self.beziers:
            bezier.path_max_speed = self.default_max_speed
        
        self._update_bezier_controls()
        self._notify_change()
    
    def _on_default_accel_change(self, value):
        """Handle default acceleration change."""
        self.default_max_accel = float(value)
        self.default_accel_label.config(text=f"{self.default_max_accel:.0f}")
        
        # Apply to all beziers
        for bezier in self.beziers:
            bezier.path_max_accel = self.default_max_accel
        
        self._update_bezier_controls()
        self._notify_change()
    
    def set_beziers(self, beziers: List[Bezier]):
        """Set the list of beziers to control."""
        self.beziers = beziers.copy()
        self._update_bezier_controls()
    
    def get_beziers(self) -> List[Bezier]:
        """Get the current list of beziers."""
        return self.beziers.copy()
    
    def set_total_time(self, total_time: float):
        """Set the total time display."""
        self.title_label.config(text=f"Velocity, Accel. Time: {total_time:.1f}s")
    
    def _update_bezier_controls(self):
        """Update the individual bezier controls."""
        # Clear existing controls
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()
        
        # Create controls for each bezier
        for i, bezier in enumerate(self.beziers):
            self._create_bezier_control(i, bezier)
    
    def _create_bezier_control(self, index: int, bezier: Bezier):
        """Create controls for a single bezier."""
        frame = ttk.Frame(self.scrollable_frame)
        frame.pack(fill=tk.X, pady=2)
        
        # Visibility toggle
        visibility_var = tk.BooleanVar(value=bezier.visible)
        visibility_button = ttk.Checkbutton(
            frame,
            variable=visibility_var,
            command=lambda: self._on_bezier_visibility_change(index, visibility_var.get())
        )
        visibility_button.pack(side=tk.LEFT, padx=2)
        
        # Stop end toggle
        stop_end_var = tk.BooleanVar(value=bezier.stop_end)
        stop_end_button = ttk.Checkbutton(
            frame,
            text="Stop",
            variable=stop_end_var,
            command=lambda: self._on_bezier_stop_end_change(index, stop_end_var.get())
        )
        stop_end_button.pack(side=tk.LEFT, padx=2)
        
        # Reversed toggle
        reversed_var = tk.BooleanVar(value=bezier.reversed)
        reversed_button = ttk.Checkbutton(
            frame,
            text="Rev",
            variable=reversed_var,
            command=lambda: self._on_bezier_reversed_change(index, reversed_var.get())
        )
        reversed_button.pack(side=tk.LEFT, padx=2)
        
        # Speed control
        speed_var = tk.DoubleVar(value=bezier.path_max_speed)
        speed_scale = ttk.Scale(
            frame,
            from_=1.0,
            to=MAX_SPEED,
            variable=speed_var,
            command=lambda v: self._on_bezier_speed_change(index, float(v)),
            length=80
        )
        speed_scale.pack(side=tk.LEFT, padx=2)
        
        speed_label = ttk.Label(frame, text=f"{bezier.path_max_speed:.0f}")
        speed_label.pack(side=tk.LEFT, padx=2)
        
        # Acceleration control
        accel_var = tk.DoubleVar(value=bezier.path_max_accel)
        accel_scale = ttk.Scale(
            frame,
            from_=1.0,
            to=MAX_ACCEL,
            variable=accel_var,
            command=lambda v: self._on_bezier_accel_change(index, float(v)),
            length=80
        )
        accel_scale.pack(side=tk.LEFT, padx=2)
        
        accel_label = ttk.Label(frame, text=f"{bezier.path_max_accel:.0f}")
        accel_label.pack(side=tk.LEFT, padx=2)
        
        # Store references for updates
        frame.speed_var = speed_var
        frame.speed_label = speed_label
        frame.accel_var = accel_var
        frame.accel_label = accel_label
        frame.visibility_var = visibility_var
        frame.stop_end_var = stop_end_var
        frame.reversed_var = reversed_var
        
        # Bind mouse enter/leave for highlighting
        frame.bind("<Enter>", lambda e: self._on_bezier_focus(index, True))
        frame.bind("<Leave>", lambda e: self._on_bezier_focus(index, False))
    
    def _on_bezier_visibility_change(self, index: int, visible: bool):
        """Handle bezier visibility change."""
        if 0 <= index < len(self.beziers):
            self.beziers[index].visible = visible
            self._update_all_visible_state()
            self._notify_change()
    
    def _on_bezier_stop_end_change(self, index: int, stop_end: bool):
        """Handle bezier stop_end change."""
        if 0 <= index < len(self.beziers):
            self.beziers[index].stop_end = stop_end
            self._notify_change()
    
    def _on_bezier_reversed_change(self, index: int, reversed_val: bool):
        """Handle bezier reversed change."""
        if 0 <= index < len(self.beziers):
            self.beziers[index].reversed = reversed_val
            self._notify_change()
    
    def _on_bezier_speed_change(self, index: int, speed: float):
        """Handle bezier speed change."""
        if 0 <= index < len(self.beziers):
            self.beziers[index].path_max_speed = speed
            # Update label
            frames = self.scrollable_frame.winfo_children()
            if index < len(frames):
                frames[index].speed_label.config(text=f"{speed:.0f}")
            self._notify_change()
    
    def _on_bezier_accel_change(self, index: int, accel: float):
        """Handle bezier acceleration change."""
        if 0 <= index < len(self.beziers):
            self.beziers[index].path_max_accel = accel
            # Update label
            frames = self.scrollable_frame.winfo_children()
            if index < len(frames):
                frames[index].accel_label.config(text=f"{accel:.0f}")
            self._notify_change()
    
    def _on_bezier_focus(self, index: int, focused: bool):
        """Handle bezier focus change for highlighting."""
        if 0 <= index < len(self.beziers):
            self.beziers[index].focused = focused
            self._notify_change()
    
    def _update_all_visible_state(self):
        """Update the all visible toggle state."""
        all_visible = all(bezier.visible for bezier in self.beziers) if self.beziers else True
        self.all_visible = all_visible
        self.visibility_var.set(all_visible)
    
    def _notify_change(self):
        """Notify that bezier properties have changed."""
        if self.on_change_callback:
            self.on_change_callback()
