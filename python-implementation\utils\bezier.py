"""
Bezier curve implementation for path planning.
"""

import math
from typing import List, Dict, Any, Tuple, Optional
from .point import Point
from config.constants import POINT_SIZE, MAX_SPEED, MAX_ACCEL


class Bezier:
    """
    Cubic <PERSON> curve with motion constraints for path planning.
    """
    
    def __init__(self, p1: Point, p2: Point, p3: Point, p4: Point,
                 path_max_speed: float = MAX_SPEED, path_max_accel: float = MAX_ACCEL,
                 stop_end: bool = False, is_straight_line: bool = False):
        """
        Initialize Bezier curve.

        Args:
            p1: Start point
            p2: First control point
            p3: Second control point
            p4: End point
            path_max_speed: Maximum speed for this segment
            path_max_accel: Maximum acceleration for this segment
            stop_end: Whether to stop at the end of this segment
            is_straight_line: Whether this should be a straight line (auto-adjusts control points)
        """
        self.p1 = p1
        self.p2 = p2
        self.p3 = p3
        self.p4 = p4
        self.reversed = False
        self.focused = False
        self.visible = True
        self.stop_end = stop_end
        self.path_max_speed = path_max_speed
        self.path_max_accel = path_max_accel
        self.is_straight_line = is_straight_line

        # If it's a straight line, adjust control points automatically
        if self.is_straight_line:
            self._make_straight_line()
    
    @classmethod
    def from_json(cls, json_data: Dict[str, Any]) -> 'Bezier':
        """Create Bezier from JSON data."""
        path_points = json_data['path']
        p1 = Point.from_json(path_points[0])
        p2 = Point.from_json(path_points[1])
        p3 = Point.from_json(path_points[2])
        p4 = Point.from_json(path_points[3])
        
        constraints = json_data['constraints']
        # Convert from m/s to in/s (multiply by 39.37)
        path_max_speed = constraints['velocity'] * 39.37
        path_max_accel = constraints['accel'] * 39.37
        
        bezier = cls(p1, p2, p3, p4, path_max_speed, path_max_accel)
        bezier.reversed = json_data['inverted']
        bezier.stop_end = json_data.get('stop_end', False)
        bezier.is_straight_line = json_data.get('is_straight_line', False)

        return bezier
    
    def to_json(self) -> Dict[str, Any]:
        """Convert to JSON-serializable dictionary."""
        return {
            "inverted": self.reversed,
            "stop_end": self.stop_end,
            "is_straight_line": self.is_straight_line,
            "path": [self.p1.to_json(), self.p2.to_json(), self.p3.to_json(), self.p4.to_json()],
            "constraints": {
                "velocity": round(self.path_max_speed / 39.37, 3),  # Convert to m/s
                "accel": round(self.path_max_accel / 39.37, 3)      # Convert to m/s²
            }
        }
    
    def evaluate(self, t: float) -> Point:
        """
        Evaluate the Bezier curve at parameter t (0 to 1).
        
        Args:
            t: Parameter from 0 to 1
            
        Returns:
            Point on the curve
        """
        # De Casteljau's algorithm
        p5 = self.p1.lerp(self.p2, t)
        p6 = self.p2.lerp(self.p3, t)
        p7 = self.p3.lerp(self.p4, t)
        p8 = p5.lerp(p6, t)
        p9 = p6.lerp(p7, t)
        return p8.lerp(p9, t)
    
    def get_control_points_screen(self, canvas_width: float, canvas_height: float) -> List[Tuple[float, float]]:
        """Get all control points in screen coordinates."""
        return [
            self.p1.get_screen_coords(canvas_width, canvas_height),
            self.p2.get_screen_coords(canvas_width, canvas_height),
            self.p3.get_screen_coords(canvas_width, canvas_height),
            self.p4.get_screen_coords(canvas_width, canvas_height)
        ]
    

    
    def get_closest_point_index(self, screen_x: float, screen_y: float, 
                               canvas_width: float, canvas_height: float) -> Optional[int]:
        """
        Get the index of the control point closest to screen coordinates.
        
        Returns:
            Point index (0-3) if within POINT_SIZE distance, None otherwise
        """
        if not self.visible:
            return None
            
        points_screen = self.get_control_points_screen(canvas_width, canvas_height)
        
        for i, (px, py) in enumerate(points_screen):
            distance = math.sqrt((px - screen_x) ** 2 + (py - screen_y) ** 2)
            if distance < POINT_SIZE:
                return i
        
        return None
    
    def is_point_over(self, screen_x: float, screen_y: float, 
                     canvas_width: float, canvas_height: float) -> bool:
        """Check if screen coordinates are over any control point."""
        return self.get_closest_point_index(screen_x, screen_y, canvas_width, canvas_height) is not None
    
    def flip(self):
        """Flip the curve across the x-axis."""
        self.p1.flip()
        self.p2.flip()
        self.p3.flip()
        self.p4.flip()
    
    def get_path_points(self, num_points: int = 50) -> List[Point]:
        """
        Get points along the Bezier curve for drawing.
        
        Args:
            num_points: Number of points to generate
            
        Returns:
            List of points along the curve
        """
        points = []
        for i in range(num_points + 1):
            t = i / num_points
            points.append(self.evaluate(t))
        return points
    
    def get_length_estimate(self, num_segments: int = 100) -> float:
        """
        Estimate the length of the Bezier curve.
        
        Args:
            num_segments: Number of segments for approximation
            
        Returns:
            Estimated length
        """
        total_length = 0.0
        prev_point = self.evaluate(0.0)
        
        for i in range(1, num_segments + 1):
            t = i / num_segments
            current_point = self.evaluate(t)
            total_length += prev_point.distance_to(current_point)
            prev_point = current_point
        
        return total_length
    
    def _make_straight_line(self):
        """Convert this Bezier curve into a straight line by adjusting control points."""
        # For a straight line, control points should be 1/3 and 2/3 along the line
        direction = self.p4.minus(self.p1)
        self.p2 = self.p1.plus(direction.times(1.0/3.0))
        self.p3 = self.p1.plus(direction.times(2.0/3.0))

    def make_straight_line(self):
        """Convert this curve to a straight line."""
        self.is_straight_line = True
        self._make_straight_line()

    def make_curved(self):
        """Convert this straight line back to a curved Bezier."""
        self.is_straight_line = False
        # Control points remain as they are - user can adjust them manually

    def move_point(self, point_index: int, delta_x: float, delta_y: float,
                   canvas_width: float, canvas_height: float) -> int:
        """
        Move a control point by screen coordinate deltas.

        Args:
            point_index: Index of point to move (0-3)
            delta_x: Screen x delta
            delta_y: Screen y delta
            canvas_width: Canvas width
            canvas_height: Canvas height

        Returns:
            Point index that was moved, or -1 if not visible
        """
        if not self.visible:
            return -1

        points = [self.p1, self.p2, self.p3, self.p4]
        if 0 <= point_index < len(points):
            points[point_index].move(delta_x, delta_y, canvas_width, canvas_height)

            # Special handling for straight lines
            if self.is_straight_line:
                if point_index == 0 or point_index == 3:
                    # If start or end point moved, recalculate control points
                    self._make_straight_line()
                    return point_index + 1
                else:
                    # If control point moved, convert to curved
                    self.is_straight_line = False
                    return point_index + 1
            else:
                # Special handling for moving start point (moves control point too)
                if point_index == 0:
                    self.p2.move(delta_x, delta_y, canvas_width, canvas_height)
                    return 1
                else:
                    return point_index + 1

        return -1

    def __str__(self) -> str:
        """String representation."""
        line_type = "StraightLine" if self.is_straight_line else "Bezier"
        return f"{line_type}(P1:{self.p1} P4:{self.p4})"
