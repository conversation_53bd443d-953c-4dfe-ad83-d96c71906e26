"""
Point class for 2D coordinate representation with field-to-screen conversions.
"""

import math
from typing import <PERSON><PERSON>, Dict, Any
from config.constants import FIELD_WIDTH


class Point:
    """
    Represents a 2D point with field coordinate system conversions.
    """
    
    def __init__(self, x: float = 0.0, y: float = 0.0):
        """Initialize a point with x, y coordinates."""
        self.x = x
        self.y = y
    
    @classmethod
    def from_screen(cls, screen_x: float, screen_y: float, canvas_width: float, canvas_height: float) -> 'Point':
        """
        Create a Point from screen coordinates.
        Converts from screen coordinates to field coordinates.
        """
        # Convert from screen to field coordinates (similar to fromOffset in Dart)
        x = -screen_y * FIELD_WIDTH / canvas_width + FIELD_WIDTH / 2
        y = -screen_x * FIELD_WIDTH / canvas_height + FIELD_WIDTH / 2
        return cls(x, y)
    
    @classmethod
    def from_json(cls, json_data: Dict[str, Any]) -> 'Point':
        """Create a Point from JSON data."""
        return cls(json_data['x'], json_data['y'])
    
    def to_json(self) -> Dict[str, Any]:
        """Convert point to JSON-serializable dictionary."""
        return {
            'x': round(self.x, 3),
            'y': round(self.y, 3)
        }
    
    def get_screen_x(self, canvas_width: float) -> float:
        """Convert field x coordinate to screen x coordinate."""
        return -self.y * canvas_width / FIELD_WIDTH + canvas_width / 2
    
    def get_screen_y(self, canvas_height: float) -> float:
        """Convert field y coordinate to screen y coordinate."""
        return -self.x * canvas_height / FIELD_WIDTH + canvas_height / 2
    
    def get_screen_coords(self, canvas_width: float, canvas_height: float) -> Tuple[float, float]:
        """Get screen coordinates as a tuple."""
        return (self.get_screen_x(canvas_width), self.get_screen_y(canvas_height))
    
    def lerp(self, other: 'Point', t: float) -> 'Point':
        """Linear interpolation between this point and another."""
        return Point(
            self.x * (1.0 - t) + other.x * t,
            self.y * (1.0 - t) + other.y * t
        )
    
    def move(self, delta_screen_x: float, delta_screen_y: float, canvas_width: float, canvas_height: float):
        """Move point by screen coordinate deltas."""
        self.x -= delta_screen_y * FIELD_WIDTH / canvas_width
        self.y -= delta_screen_x * FIELD_WIDTH / canvas_height
    
    def minus(self, other: 'Point') -> 'Point':
        """Subtract another point from this point."""
        return Point(self.x - other.x, self.y - other.y)
    
    def plus(self, other: 'Point') -> 'Point':
        """Add another point to this point."""
        return Point(self.x + other.x, self.y + other.y)
    
    def times(self, multiplier: float) -> 'Point':
        """Multiply point coordinates by a scalar."""
        return Point(self.x * multiplier, self.y * multiplier)
    
    def midpoint(self, other: 'Point') -> 'Point':
        """Get the midpoint between this point and another."""
        return Point((self.x + other.x) / 2.0, (self.y + other.y) / 2.0)
    
    def magnitude(self) -> float:
        """Get the magnitude (length) of the vector from origin to this point."""
        return math.sqrt(self.x ** 2 + self.y ** 2)
    
    def norm(self) -> 'Point':
        """Get the normalized (unit) vector."""
        mag = self.magnitude()
        if mag == 0:
            return Point(0, 0)
        return Point(self.x / mag, self.y / mag)
    
    def flip(self):
        """Flip the y coordinate (mirror across x-axis)."""
        self.y = -self.y
    
    def distance_to(self, other: 'Point') -> float:
        """Calculate distance to another point."""
        return self.minus(other).magnitude()
    
    def __str__(self) -> str:
        """String representation of the point."""
        return f"x: {self.x:.1f}, y: {self.y:.1f}"
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return f"Point({self.x}, {self.y})"
    
    def __eq__(self, other) -> bool:
        """Check equality with another point."""
        if not isinstance(other, Point):
            return False
        return abs(self.x - other.x) < 1e-6 and abs(self.y - other.y) < 1e-6
